/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/breakpoints.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/


/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/variables.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/* Colors */
/* End Colors */

/* SPACING */
/* END SPACING */

/* FONT SIZES */
/* END FONT SIZES */

/*FONT WEIGHTS */
/* END FONT WEIGHTS */
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationQuestionAndAnswers/CloudMigrationQuestionAndAnswers.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationQuestionAndAnswers_container__23uHh {
  display: flex;
  max-width: 1192px;
  gap: 20px;
  flex-direction: column;
}

.CloudMigrationQuestionAndAnswers_question_container__GaNjn {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  gap: 10px;
  padding: 20px;

  @media screen and (max-width: 1024px) {
    width: 100%;
  }
}

.CloudMigrationQuestionAndAnswers_question_number__C7Rlp {
  font-weight: 600;
  font-size: 20px;
  line-height: 148%;
}

.CloudMigrationQuestionAndAnswers_sub_question_name__bSje1 {
  min-width: 100%;
  font-weight: 600;
  font-size: 18px;
  line-height: 164%;
}

.CloudMigrationQuestionAndAnswers_question_name__5tq0J {
  width: -moz-fit-content;
  width: fit-content;
  font-weight: 600;
  font-size: 20px;
  line-height: 148%;
}

.CloudMigrationQuestionAndAnswers_error_message__jiAyq {
  color: #ff0000;
}

.CloudMigrationQuestionAndAnswers_mcqs_container__alqLv {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  @media screen and (max-width: 1024px) {
    padding: 20px;
  }
}

.CloudMigrationQuestionAndAnswers_selected_mcq__lkerl {
  background-image: linear-gradient(#F3F3F3, #F3F3F3),
    linear-gradient(
      93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A {
  background-color: #F3F3F3;
  width: -moz-fit-content;
  width: fit-content;
  padding: 12px;
  border-radius: 12px;
  font-weight: 400;
  font-size: 16px;
  line-height: 160%;
  position: relative;
  border: 2px solid transparent;
  cursor: pointer;
}

/* Support both radio and checkbox inputs */
.CloudMigrationQuestionAndAnswers_mcq__hzM2A > input[type='radio'],
.CloudMigrationQuestionAndAnswers_mcq__hzM2A > input[type='checkbox'] {
  display: none;
}

/* Cloud migration specific styling for multi-select questions */
.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1 {
  position: relative;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  border: 2px solid #666;
  border-radius: 3px;
  background: transparent;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1.CloudMigrationQuestionAndAnswers_selected_mcq__lkerl::after {
  background: #30ad43;
  border-color: #30ad43;
}

.CloudMigrationQuestionAndAnswers_mcq__hzM2A.CloudMigrationQuestionAndAnswers_multi_select__6Y7F1.CloudMigrationQuestionAndAnswers_selected_mcq__lkerl::before {
  content: '✓';
  position: absolute;
  top: 6px;
  right: 10px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  z-index: 2;
}

.CloudMigrationQuestionAndAnswers_draggable_container__1Y__p {
  width: -webkit-fill-available;
  height: min-content;
  background-color: #F3F3F3;
  padding: 20px;
  border-radius: 12px;
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t {
  -webkit-appearance: none;
  width: -webkit-fill-available;
  height: 7px;
  background: transparent;
  border-radius: 3px;
  outline: none;
  margin: 20px 30px;
  cursor: pointer;

  @media screen and (max-width: 1200px) {
    width: 90%;
  }
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-webkit-slider-runnable-track,
.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-moz-range-track {
  height: 7px;
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #30ad43;
  border: none;
  cursor: pointer;
}

.CloudMigrationQuestionAndAnswers_draggable_input__xnr9t::-moz-range-thumb {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #30ad43;
  border: none;
  cursor: pointer;
}

.CloudMigrationQuestionAndAnswers_draggable_wrapper__RlBQV {
  display: flex;
  justify-content: space-between;
}

.CloudMigrationQuestionAndAnswers_draggable_label__QSi_F {
  cursor: pointer;
  text-align: center;
  width: 127px;
  height: 44px;
}

.CloudMigrationQuestionAndAnswers_selected_draggable_label__YiCHA {
  background: linear-gradient(
    to right,
    #FEBE10,
    #F47A37,
    #F05443,
    #D91A5F,
    #B41F5E
  );
  -webkit-background-clip: text;
          background-clip: text;
  color: transparent;
}

.CloudMigrationQuestionAndAnswers_number_wrapper__h26KF {
  display: flex;
  flex-direction: column;
}

.CloudMigrationQuestionAndAnswers_draggable_container_tablet___3apO {
  display: flex;
  flex-direction: row;
  padding: 20px;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf {
  width: 20%;
  height: 62px;
  padding: 10px;
  border: 0.5px solid #8c8b8b;
  font-weight: 500;
  font-size: 26px;
  line-height: 164%;
  text-align: center;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf:first-child {
  border-radius: 10px 0 0 10px;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf:last-child {
  border-radius: 0 10px 10px 0;
}

.CloudMigrationQuestionAndAnswers_number_label__AasKk {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.CloudMigrationQuestionAndAnswers_number_label__AasKk > span {
  text-align: center;
  width: 20%;
}

.CloudMigrationQuestionAndAnswers_selected_number__w7Tob {
  background-color: #30ad43;
}

.CloudMigrationQuestionAndAnswers_number__hjPYf > input[type='radio'] {
  display: none;
}

/* Cloud migration specific enhancements */
.CloudMigrationQuestionAndAnswers_cloud_migration_container__gOkFf {
  border-left: 4px solid #30ad43;
  padding-left: 16px;
}

.CloudMigrationQuestionAndAnswers_cost_indicator__0MgM9 {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin-top: 4px;
}

.CloudMigrationQuestionAndAnswers_required_field__2pyY6 {
  color: #ff0000;
}

.CloudMigrationQuestionAndAnswers_required_field__2pyY6::after {
  content: ' *';
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/VideoModal/VideoModal.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
.VideoModal_respcontainer__rM1yN {
  position: relative;
  overflow: hidden;
  padding-top: 56.25%;
}

.VideoModal_respiframe__0Aj7P {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.VideoModal_modal-header__klYOg {
  border-bottom: 0;
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Breadcrumb/Breadcrumb.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
.Breadcrumb_breadcrumb__Q0xQA {

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.Breadcrumb_breadcrumb__Q0xQA a {
  color: #FFFFFF;
  text-decoration: none;
}

.Breadcrumb_arrow_style___ICIk {
  color: #FFFFFF;
}
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/styles/typography.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
.typography_h1__DecPZ {
  font-size: 78px;

  font-weight: 700;

  @media (max-width: 450px) {
    font-size: 48px;
  }
}

.typography_h2__Dn0zf {
  font-size: 64px;

  @media (max-width: 450px) {
    font-size: 44px;
  }
}

.typography_h3__o3Abb {
  font-size: 52px;

  @media (max-width: 450px) {
    font-size: 40px;
  }
}

.typography_h4__lGrWj {
  font-size: 40px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 28px;
  }
}

.typography_h5__DGJHL {
  font-size: 32px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 22px;
  }
}

.typography_h6__vf_A0 {
  font-size: 24px;
  font-weight: 600;

  @media (max-width: 450px) {
    font-size: 18px;
  }
}

.typography_caption__hfk0A {
  font-size: 12px;
  line-height: 1.67;
  font-weight: normal;
  letter-spacing: 0.01em;
  max-width: 50em;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Heading/Heading.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.Heading_center__XBGsG {
  text-align: center;
}
.Heading_left__ouHog {
  text-align: left;
}
.Heading_right__jsN_Y {
  text-align: right;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CircularButtonWithArrow/CircularButtonWithArrow.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
.CircularButtonWithArrow_container__9Cvr1 {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.CircularButtonWithArrow_circle__H7jjo {
  position: relative;
  width: 100%;
  height: 100%;
}

.CircularButtonWithArrow_arrow__h3ojH {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease-in-out;
}

.CircularButtonWithArrow_container__9Cvr1:hover .CircularButtonWithArrow_arrow__h3ojH {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.CircularButtonWithArrow_arrowImage__G7E_X {
  width: 100%;
  height: 100%;
}

.CircularButtonWithArrow_arrow_scroll__a_DTi {
  position: absolute;
  top: 33%;
  left: 21%;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/Button/Button.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.Button_button__exqP_,
.Button_link__9n7Et {
  position: relative;
  background-color: #000000;
  background-image: linear-gradient(#000000, #000000),
    linear-gradient(93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  font-size: 14px;
  font-weight: 600;
  padding: 8px 14px;
  cursor: pointer;
  line-height: 21px;
  letter-spacing: 0.2px;
  color: #FFFFFF;
  transition: 0.2s linear;
  min-width: 120px;
  min-height: 40px;
  border: 2px solid transparent;
  /* Required to create the border effect */
  border-radius: 3px;
}


.Button_button__exqP_:hover {

  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.Button_innerWrapper__ITLB1 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.Button_leftWrapper__fWtI9 {
  margin-right: 8px;
}

.Button_rightWrapper__GkIh_ {
  margin-left: 8px;
}

.Button_link__9n7Et {
  display: inline-block;
  color: #FFFFFF;
  text-decoration: none;
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/ImageWithBlurPreview/ImageWithBlurPreview.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
.ImageWithBlurPreview_image_hidden__8NnZq {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.ImageWithBlurPreview_image_visible___YMAQ {
  opacity: 1;
}

.ImageWithBlurPreview_mobile_image__1Vbfb {
  object-fit: cover;
  object-position: center center;
  
  @media screen and (max-width: 800px) {
    object-position: center 20%;
  }
}

/* Show desktop images only on desktop screens when mobile image is available */
.ImageWithBlurPreview_desktop_only__JS73q {
  display: block;
  
  @media screen and (max-width: 1023px) {
    display: none !important;
  }
}

/* Show mobile images only on mobile and tablet screens */
.ImageWithBlurPreview_mobile_tablet_only__y4ZJ9 {
  display: none;

  @media screen and (max-width: 1023px) {
    display: block;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/HeroSection/HeroSection.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
.HeroSection_main_container__a_G6M {
  position: relative;
  overflow: hidden;

  /* Ensure proper height for mobile images */
  @media screen and (max-width: 1023px) {
    min-height: 500px;
  }

  @media screen and (max-width: 767px) {
    min-height: 450px;
  }
}

.HeroSection_inner_container__DZ5z1 {
  display: flex;
  flex-direction: column;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 2000px) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.HeroSection_inner_container_resources__1RjK7 {
  display: flex;
  flex-direction: column;
  gap: 74px;
  min-height: 441px;
  max-width: 803px;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 768px) {
    padding: 20px 30px 0 30px;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px;
  }
}

.HeroSection_inner_container_partners__6Ff8X {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 167px;
  min-height: 741px;
  max-width: 50%;
  padding: 20px 0 0 120px;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 2000px) {
    max-width: 70%;
  }

  @media screen and (max-width: 768px) {
    max-width: 100%;
    padding: 20px 30px 0 30px !important;
  }

  @media screen and (max-width: 450px) {
    padding: 20px 16px 0 16px !important;
  }
}

.HeroSection_section__nzkUY {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.HeroSection_section_without_breadcrumbs__SJ09S {
  margin-top: 188px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  font-size: 20px;
}

.HeroSection_tag__ZxYj0 {
  color: #FFFFFF;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background-color: #000000;
  padding: 2px 6px;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: 3px;
}

.HeroSection_background_image__CGewD {
  background-color: #000000;
  object-fit: cover;
  transition: transform 0.5s ease;
  z-index: -1;

  /* Responsive object-fit for mobile and tablet screens to prevent cutoff */
  @media screen and (max-width: 1023px) {
    object-fit: cover;
    object-position: center top;
  }

  /* For very small mobile screens, ensure full image visibility */
  @media screen and (max-width: 800px) {
    object-fit: fill;
    object-position: center center;
  }
}

.HeroSection_main_container__a_G6M:hover .HeroSection_background_image__CGewD {
  transform: scale(1.1);
}

.HeroSection_title__im1kR > h1 {
  font-size: 78px;
  font-style: normal;
  font-weight: 600;
  line-height: 113%;
  color: #FFFFFF;

  @media screen and (max-width: 768px) {
    font-size: 64px;
    line-height: 76.8px;
    letter-spacing: 1.28px;
  }

  @media screen and (max-width: 450px) {
    font-size: 44px;
    line-height: 118%;
    letter-spacing: -1.76px;
  }
}

.HeroSection_hero_desc__ui48U {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  color: #FFFFFF;
}

.HeroSection_btn__6f4Xt {
  height: 62px;
  width: 192px;
  font-size: 20px;
  padding: 16px 36px;
}

.HeroSection_inner_container_ai_readiness__FsE8Q {
  max-width: 60%;
  padding: 70px 0 105px 138px;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;

  @media screen and (max-width: 1440px) {
    padding: 0 0 91px 112px;
  }

  @media screen and (max-width: 1024px) {
    max-width: 100%;
    padding: 24px 20px;
  }

  @media screen and (max-width: 450px) {
    padding: 24px 4px;
  }
}

.HeroSection_heading__jTq5w > h1 {
  font-weight: 700;
  font-size: 48px;
  line-height: 129%;
  color: #FFFFFF;
}

.HeroSection_description__tUvZp {
  font-weight: 400;
  font-size: 22px;
  line-height: 160%;
  color: #FFFFFF;
}

.HeroSection_description__tUvZp > p {
  margin: 0;
}

.HeroSection_cta__drDMW {
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  width: 260px;
  height: 62px;
  padding: 16px 36px;
  border-radius: 6px;
  border-width: 2px;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationStep/CloudMigrationStep.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationStep_container__2i9Rk {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 824px;
  margin: 0 auto;
  align-items: flex-start;
  padding-bottom: 40px;
}

.CloudMigrationStep_stepWrapper__oYD_I {
  display: flex;
  flex-direction: column;
  gap: 9px;
  align-items: center;
  position: relative;
}

.CloudMigrationStep_circle__KlE2K {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 1px solid #646464;
  color: #646464;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 400;
  z-index: 1;
  cursor: not-allowed;
}

.CloudMigrationStep_active_circle__Scn1z {
  color: #fff !important;
  border-color: #f05443 !important;
  background-color: #f05443;
  cursor: pointer;
}

.CloudMigrationStep_selected_circle__YOG19 {
  color: #fff !important;
  border-color: #f05443 !important;
  background-color: #f05443;
  cursor: pointer;
}

.CloudMigrationStep_label__RepqJ {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 139%;
  width: 128px;
  height: 42px;
  text-align: center;
  color: #000000;
  margin-bottom: 0;
  cursor: not-allowed;
}

.CloudMigrationStep_active_text__O2dRR {
  color: #f05443 !important;
  cursor: pointer;
}

.CloudMigrationStep_selected_text__0iTq5 {
  color: #f05443 !important;
  cursor: pointer;
}

.CloudMigrationStep_line__0Ht5Y {
  height: 2px;
  width: 121px;
  background-color: #646464;
  position: absolute;
  left: calc(100% + -38px);
  top: 24%;
  z-index: 0;

  @media screen and (max-width: 820px) {
    width: 13.5vw;
    left: 12vw;
  }
}

.CloudMigrationStep_line__0Ht5Y.CloudMigrationStep_active__zaJ8U {
  background-color: #f05443;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationForm/CloudMigrationForm.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationForm_heading__1G_N7 > h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px 0;
  border-bottom: 2px solid black;
}

.CloudMigrationForm_formWrapper__pHGgB {
  padding: 40px 10px 0;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (max-width: 1170px) {
    padding: 2.5rem 2rem;
  }

  @media (max-width: 450px) {
    padding: 1rem;
  }
}

.CloudMigrationForm_form__M10y8 {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (min-width: 1400px) {
    width: 1192px;
  }
}

.CloudMigrationForm_formFields__OXHvQ {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.CloudMigrationForm_personalDetailsWrapper__a060Z {
  display: flex;
  gap: 24px;
  flex-direction: column;
}

.CloudMigrationForm_row__nWV1a {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  row-gap: 40px;
}

.CloudMigrationForm_nameAndInputWrapper__VZY9z {
  width: 48%;
  display: flex;
  justify-content: space-between;

  @media screen and (max-width: 1170px) {
    width: 100%;
    padding: 0 5rem;
  }

  @media screen and (max-width: 1024px) {
    width: 100%;
    padding: 0;
  }
}

.CloudMigrationForm_firstRow__RAeZ0 {
  flex-direction: row;

  @media (max-width: 1170px) {
    flex-direction: column !important;
    gap: 24px;
  }
}

.CloudMigrationForm_formLabel__k18br {
  width: 220px;
  color: #000000;
  font-size: 26px;
  font-style: normal;
  font-weight: 500;
  line-height: 164%;

  @media screen and (max-width: 768px) {
    width: 170px;
    font-weight: 400;
    font-size: 18px;
    line-height: 168%;
  }
}

.CloudMigrationForm_formInput__DF52I {
  width: 300px;
  background: #F3F3F3;
  color: #000000;
  border-radius: 3px;
  height: 41px;
  border: none;
  padding: 10px;

  @media screen and (max-width: 1170px) {
    width: 68%;
  }

  @media screen and (max-width: 450px) {
    width: 48%;
  }
}

.CloudMigrationForm_formInput__DF52I:focus-visible {
  outline: none;
  margin: 0;
}

.CloudMigrationForm_errorInput__jl0Kd {
  border: 1px solid #ff0000 !important;
}

.CloudMigrationForm_errorLabel__V_39N {
  color: #ff0000;
  font-size: 26px;

  @media screen and (max-width: 768px) {
    width: 170px;
    font-weight: 400;
    font-size: 18px;
    line-height: 168%;
  }
}

.CloudMigrationForm_consentRow__7Aq_P {
  display: flex;
  gap: 12px;
}

.CloudMigrationForm_consentText__Ck9OH {
  display: flex;
  gap: 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: pointer;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.CloudMigrationForm_consentText__Ck9OH > input {
  border-radius: 2px;
}

.CloudMigrationForm_errorLabel_consentText__y9qso {
  color: #ff0000;
  font-size: 16px;
}

.CloudMigrationForm_submitButtonRow__lQ01D {
  align-items: center;
  justify-content: start;
  gap: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 24px;
  }
}

.CloudMigrationForm_result_button__PQm4i {
  padding: 13px 35.5px;
  border: none !important;

  font-weight: 600;
  font-size: 20px;
  line-height: 148%;

  color: #FFFFFF;
  background: linear-gradient(
    90deg,
    #FEBE10 0%,
    #F47A37 30.56%,
    #F05443 53.47%,
    #D91A5F 75.75%,
    #B41F5E 100%
  );
}

.CloudMigrationForm_container_spinner__PcbTP {
  position: relative;
  width: 170px;
  height: 68px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  background-image: linear-gradient(#FFFFFF, #FFFFFF),
    linear-gradient(
      93.12deg,
      #FEBE10 0%,
      #F47A37 30.56%,
      #F05443 53.47%,
      #D91A5F 75.75%,
      #B41F5E 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;
}

.CloudMigrationForm_spinner__yw13I {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(
    93deg,
    #febe10 0%,
    #f47a37 30.56%,
    #f05443 53.47%,
    #d91a5f 75.75%,
    #b41f5e 100%
  );

  -webkit-mask-image: radial-gradient(
    circle,
    rgba(0, 0, 0, 0) 55%,
    rgba(0, 0, 0, 1) 60%
  );
  animation: CloudMigrationForm_spin___RViF 0.8s linear infinite;
}

@keyframes CloudMigrationForm_spin___RViF {
  to {
    transform: rotate(360deg);
  }
}

.CloudMigrationForm_errorMessages__8BkHm {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

@media screen and (max-width: 1024px) {
  .CloudMigrationForm_form_container__5eCEW {
    padding: 24px 16px;
  }
  
  .CloudMigrationForm_form_heading__lqTFY h3 {
    font-size: 24px;
  }
  
  .CloudMigrationForm_form_heading__lqTFY p {
    font-size: 14px;
  }
  
  .CloudMigrationForm_submit_button__syQm4 {
    padding: 14px 32px;
    font-size: 14px;
    min-width: 180px;
  }

  .CloudMigrationForm_result_button__PQm4i {
    padding: 14px 32px;
    font-size: 14px;
    min-width: 180px;
  }

  .CloudMigrationForm_spinner__yw13I {
    width: 32px;
    height: 32px;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationResults/CloudMigrationResults.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationResults_results_wrapper__2OJ5b {
  max-width: 1192px;
  margin: 0 auto;
  padding: 40px 20px;
  text-align: center;

  @media screen and (max-width: 1192px) {
    margin: 0 36px;
  }

  @media screen and (max-width: 1024px) {
    padding: 20px 16px;
  }
}

/* Restart Button */
.CloudMigrationResults_restart_button_wrapper__ERGxd {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 40px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 20px;
  }
}

.CloudMigrationResults_restart_button__FGauo {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 12px 24px !important;
  border: 2px solid #FEBE10 !important;
  border-radius: 3px !important;
  background-color: #FFFFFF !important;
  color: #FEBE10 !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
}

.CloudMigrationResults_restart_button__FGauo:hover {
  background-color: #FEBE10 !important;
  color: #FFFFFF !important;
}

/* Main Heading */
.CloudMigrationResults_results_header__1vFJ7 {
  margin-bottom: 40px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 30px;
  }
}

.CloudMigrationResults_main_heading__71q_N {
  font-size: 48px;
  font-weight: 700;
  color: #000000;
  line-height: 1.2;
  margin: 0;

  @media screen and (max-width: 1024px) {
    font-size: 36px;
  }

  @media screen and (max-width: 576px) {
    font-size: 28px;
  }
}

/* Cost Display */
.CloudMigrationResults_cost_display__8nqav {
  margin-bottom: 40px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 30px;
  }
}

.CloudMigrationResults_cost_range__xXt9p {
  font-size: 64px;
  font-weight: 700;
  color: #FF6B6B;
  line-height: 1.1;
  margin-bottom: 20px;

  @media screen and (max-width: 1024px) {
    font-size: 48px;
  }

  @media screen and (max-width: 576px) {
    font-size: 36px;
  }
}

/* Disclaimer */
.CloudMigrationResults_disclaimer__I_zIB {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 60px;
  text-align: left;

  @media screen and (max-width: 1024px) {
    padding: 20px;
    margin-bottom: 40px;
  }
}

.CloudMigrationResults_disclaimer__I_zIB p {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  color: #333;

  @media screen and (max-width: 1024px) {
    font-size: 14px;
  }
}

/* Savings Section */
.CloudMigrationResults_savings_section__aF53V {
  margin-bottom: 60px;

  @media screen and (max-width: 1024px) {
    margin-bottom: 40px;
  }
}

.CloudMigrationResults_savings_heading__uY5Sd {
  font-size: 40px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 24px;
  line-height: 1.2;

  @media screen and (max-width: 1024px) {
    font-size: 32px;
    margin-bottom: 20px;
  }

  @media screen and (max-width: 576px) {
    font-size: 28px;
  }
}

.CloudMigrationResults_savings_description__C5PTN {
  font-size: 18px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media screen and (max-width: 1024px) {
    font-size: 16px;
    margin-bottom: 30px;
  }
}

/* Benefits Grid */
.CloudMigrationResults_benefits_grid__zBdVQ {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: 30px;
  gap: 30px;
  margin-bottom: 50px;

  @media screen and (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 40px;
  }
}

.CloudMigrationResults_benefit_card__CD8K0 {
  background-color: #FFFFFF;
  padding: 32px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  text-align: left;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  @media screen and (max-width: 1024px) {
    padding: 24px 20px;
  }
}

.CloudMigrationResults_benefit_card__CD8K0:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.CloudMigrationResults_benefit_title__tyMAr {
  font-size: 20px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 16px;
  line-height: 1.3;

  @media screen and (max-width: 1024px) {
    font-size: 18px;
    margin-bottom: 12px;
  }
}

.CloudMigrationResults_benefit_description__O1eoR {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin: 0;

  @media screen and (max-width: 1024px) {
    font-size: 14px;
  }
}

/* CTA Button */
.CloudMigrationResults_cta_button_wrapper__JCK_7 {
  display: flex;
  justify-content: center;
}

.CloudMigrationResults_cta_button__1i69Z {
  background: linear-gradient(135deg, #FEBE10 0%, #F47A37 100%) !important;
  color: #FFFFFF !important;
  border: none !important;
  padding: 16px 40px !important;
  border-radius: 8px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-block !important;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;

  @media screen and (max-width: 1024px) {
    padding: 14px 32px !important;
    font-size: 16px !important;
  }
}

.CloudMigrationResults_cta_button__1i69Z:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4) !important;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[7].use[3]!./src/components/CloudMigrationBody/CloudMigrationBody.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.CloudMigrationBody_container__1XSk8 {
  padding: 36px 0 80px 0;

  @media screen and (max-width: 1024px) {
    padding: 36px 0 36px 0;
  }
}

.CloudMigrationBody_step_container__I68x2 {
  height: 45px;
  padding: 16px;
  display: flex;
  gap: 10px;

  font-weight: 500;
  font-size: 18px;
  line-height: 160%;
  letter-spacing: 0.36;
}

.CloudMigrationBody_hidden__Tr03l {
  display: none;
}

.CloudMigrationBody_section_wrapper__Ur4lt {
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 0 10px;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.CloudMigrationBody_heading__Wkgto>h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px;
  border-bottom: 2px solid black;
}

.CloudMigrationBody_button_wrapper__t4Guz {
  display: flex;
  justify-content: flex-end;
  gap: 40px;
  align-items: center;
  margin-top: 40px;
}

.CloudMigrationBody_button_wrapper__t4Guz>button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.CloudMigrationBody_button_wrapper__t4Guz>button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.CloudMigrationBody_button_wrapper_mobile__0rVi2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 16px;
}

.CloudMigrationBody_button_wrapper_mobile__0rVi2>button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.CloudMigrationBody_button_wrapper_mobile__0rVi2>button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.CloudMigrationBody_error_message__0B5r4 {
  color: #ff5656;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  padding: 10px;
  background-color: #fff5f5;
  border: 1px solid #ff5656;
  border-radius: 4px;
}

.CloudMigrationBody_results_wrapper__CIlvG {
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 40px 10px;
  text-align: center;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.CloudMigrationBody_cost_display__bqWSP {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 16px;
  margin: 20px 0;
}

.CloudMigrationBody_cost_range__74WFe {
  font-size: 48px;
  font-weight: 700;
  margin: 20px 0;
}

.CloudMigrationBody_cost_description__PnWei {
  font-size: 18px;
  line-height: 1.6;
  margin: 20px 0;
}

.CloudMigrationBody_consultation_button__o4t8M {
  margin: 20px auto;
  padding: 16px 32px;
  background-color: #FEBE10;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.CloudMigrationBody_consultation_button__o4t8M:hover {
  background-color: #F47A37;
}

.CloudMigrationBody_restart_button__69z5k {
  margin: 20px auto;
  padding: 12px 24px;
  background-color: transparent;
  color: #FEBE10;
  border: 2px solid #FEBE10;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.CloudMigrationBody_restart_button__69z5k:hover {
  background-color: #FEBE10;
  color: white;
}

@media screen and (max-width: 1024px) {
  .CloudMigrationBody_cost_range__74WFe {
    font-size: 36px;
  }
  
  .CloudMigrationBody_cost_description__PnWei {
    font-size: 16px;
  }
  
  .CloudMigrationBody_heading__Wkgto>h2 {
    font-size: 24px;
    padding: 16px;
  }
}

